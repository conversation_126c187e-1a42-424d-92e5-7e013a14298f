import ComposableArchitecture
import SwiftUI

@Reducer
public struct Home {
    @ObservableState
    public struct State: Equatable {
        // Define a structure for feature data to make it more organized and reusable
        public struct Feature: Equatable, Identifiable {
            public let id = UUID()
            let title: String
            let description: String
            let systemImage: String
            let color: Color
        }
        
        // Populate a list of features. This could be loaded from a data source in a real app.
        public var features: [Feature] = [
            Feature(
                title: "Feature 1",
                description: "This is a sample feature card with description",
                systemImage: "star.fill",
                color: .blue
            ),
            Feature(
                title: "Feature 2",
                description: "Another feature card with different styling",
                systemImage: "heart.fill",
                color: .red
            ),
            Feature(
                title: "Feature 3",
                description: "Third feature showcasing app capabilities",
                systemImage: "bolt.fill",
                color: .orange
            )
        ]
        
        public init() {}
    }
    
    // Make Action Equatable
    public enum Action: Equatable {
        case featureCardTapped(Home.State.Feature.ID) // Use Feature ID for clarity
    }
    
    public var body: some ReducerOf<Self> {
        Reduce { state, action in
            switch action {
            case let .featureCardTapped(featureId):
                // Find the feature by ID if needed for specific logic
                if let tappedFeature = state.features.first(where: { $0.id == featureId }) {
                    // Handle feature card tap - could navigate or show details
                    print("Tapped on \(tappedFeature.title)")
                }
                return .none
            }
        }
    }
}

// MARK: - HomeView

public struct HomeView: View {
    let store: StoreOf<Home>
    
    public init(store: StoreOf<Home>) {
        self.store = store
    }
    
    public var body: some View {
        WithPerceptionTracking {
            ScrollView {
                    VStack(spacing: 20) {
                        // Header
                        HStack {
                            Text("Welcome to Template App")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                            Spacer()
                        }
                        .padding(.horizontal)
                        
                        // Feature Cards - Iterate over the features array
                        LazyVStack(spacing: 16) {
                            ForEach(store.features) { feature in
                                Button(action: {
                                    store.send(.featureCardTapped(feature.id))
                                }) {
                                    FeatureCard(
                                        title: feature.title,
                                        description: feature.description,
                                        systemImage: feature.systemImage,
                                        color: feature.color
                                    )
                                }
                                .buttonStyle(PlainButtonStyle()) // Keep plain button style for custom card appearance
                            }
                        }
                        .padding(.horizontal)
                        
                        Spacer()
                    }
                }
                .navigationTitle("Home")
                .navigationBarTitleDisplayMode(.large)
        }
    }
}

// MARK: - Feature Card

public struct FeatureCard: View {
    let title: String
    let description: String
    let systemImage: String
    let color: Color
    
    public init(title: String, description: String, systemImage: String, color: Color) {
        self.title = title
        self.description = description
        self.systemImage = systemImage
        self.color = color
    }
    
    public var body: some View {
        HStack(spacing: 16) {
            Image(systemName: systemImage)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(color.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(radius: 2, x: 0, y: 1)
    }
}

#Preview {
    FeatureCard(
        title: "Sample Feature",
        description: "This is a sample feature card with description",
        systemImage: "star.fill",
        color: .blue
    )
    .padding()
}

#Preview {
    HomeView(
        store: Store(initialState: Home.State()) {
            Home()
        }
    )
}
