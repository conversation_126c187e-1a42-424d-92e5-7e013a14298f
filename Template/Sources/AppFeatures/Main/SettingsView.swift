import SwiftUI
import ComposableArchitecture

@Reducer
public struct Settings {
    @ObservableState
    public struct State: Equatable {
        public var isDarkModeEnabled: Bool = false
        public var notificationsEnabled: Bool = true
        // Consider adding user/account info here if needed in the future
        public init() {}
    }
    
    public enum Action: Equatable {
        case darkModeToggled(Bool)
        case notificationsToggled(Bool)
        case helpSupportTapped
        case aboutTapped
        case signOutTapped
    }
    
    public init() {}
    
    public var body: some ReducerOf<Self> {
        Reduce { state, action in
            switch action {
            case let .darkModeToggled(isOn):
                state.isDarkModeEnabled = isOn
                return .none
            case let .notificationsToggled(isOn):
                state.notificationsEnabled = isOn
                return .none
            case .helpSupportTapped:
                // Handle help & support navigation
                return .none
            case .aboutTapped:
                // Handle about navigation
                return .none
            case .signOutTapped:
                // Handle sign out logic
                return .none
            }
        }
    }
}

// MARK: - SettingsView

public struct SettingsView: View {
    let store: StoreOf<Settings>
    
    public init(store: StoreOf<Settings>) {
        self.store = store
    }
    
    public var body: some View {
        WithPerceptionTracking {
            List {
                Section("Preferences") {
                    Toggle(isOn: Binding(
                        get: { store.isDarkModeEnabled },
                        set: { store.send(.darkModeToggled($0)) }
                    )) {
                        Label("Dark Mode", systemImage: "moon")
                    }
                    Toggle(isOn: Binding(
                        get: { store.notificationsEnabled },
                        set: { store.send(.notificationsToggled($0)) }
                    )) {
                        Label("Notifications", systemImage: "bell")
                    }
                }
                
                Section("Support") {
                    Button(action: {
                        store.send(.helpSupportTapped)
                    }) {
                        Label("Help & Support", systemImage: "questionmark.circle")
                    }
                    .foregroundColor(.primary)
                    
                    NavigationLink {
                        WithPerceptionTracking {
                            AboutView(
                                store: Store(initialState: About.State()) {
                                    About()
                                }
                            )
                        }
                    } label: {
                        Label("About", systemImage: "info.circle")
                    }
                }
                
                Section("Account") {
                    Button(action: {
                        store.send(.signOutTapped)
                    }) {
                        Label("Sign Out", systemImage: "rectangle.portrait.and.arrow.right")
                            .foregroundColor(.red)
                        Spacer()
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

#Preview {
    SettingsView(
        store: Store(initialState: Settings.State()) {
            Settings()
        }
    )
}
