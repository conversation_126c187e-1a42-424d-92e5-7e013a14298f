import SwiftUI
import ComposableArchitecture

@Reducer
public struct Profile {
    @ObservableState
    public struct State: Equatable {
        public var userName: String = "John Doe"
        public var userEmail: String = "<EMAIL>"
        public var notificationsEnabled: Bool = true
        
        public init() {}
    }
    
    // Make Action Equatable for consistency and testability
    public enum Action: Equatable {
        case notificationsTapped
        case editProfileTapped
    }
    
    public init() {}
    
    public var body: some ReducerOf<Self> {
        Reduce { state, action in
            switch action {
            case .notificationsTapped:
                // Handle notifications setup
                return .none
                
            case .editProfileTapped:
                // Handle edit profile navigation
                return .none
            }
        }
    }
}

// MARK: - ProfileHeaderView

struct ProfileHeaderView: View {
    let userName: String
    let userEmail: String
    // Optional: Add profileImage related properties if it becomes dynamic

    var body: some View {
        VStack(spacing: 8) {
            Circle()
                .fill(Color.blue.gradient)
                .frame(width: 120, height: 120)
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                )
            
            Text(userName)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(userEmail)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - ProfileActionButton

struct ProfileActionButton: View {
    let title: String
    let systemImage: String
    let backgroundColor: Color
    let foregroundColor: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: systemImage)
                Text(title)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .cornerRadius(10)
        }
    }
}

// MARK: - ProfileView

public struct ProfileView: View {
    let store: StoreOf<Profile>
    
    public init(store: StoreOf<Profile>) {
        self.store = store
    }
    
    public var body: some View {
        WithPerceptionTracking {
            VStack(spacing: 24) {
                    ProfileHeaderView(
                        userName: store.userName,
                        userEmail: store.userEmail
                    )
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        ProfileActionButton(
                            title: "Enable Notifications",
                            systemImage: "bell",
                            backgroundColor: .blue,
                            foregroundColor: .white,
                            action: { store.send(.notificationsTapped) }
                        )
                        
                        ProfileActionButton(
                            title: "Edit Profile",
                            systemImage: "pencil",
                            backgroundColor: Color.gray.opacity(0.1),
                            foregroundColor: .primary,
                            action: { store.send(.editProfileTapped) }
                        )
                    }
                    .padding(.horizontal)
                    
                    Spacer()
                }
                .navigationTitle("Profile")
                .navigationBarTitleDisplayMode(.large)
        }
    }
}

#Preview {
    ProfileView(
        store: Store(initialState: Profile.State()) {
            Profile()
        }
    )
}

#Preview("ProfileHeaderView") {
    ProfileHeaderView(userName: "Jane Doe", userEmail: "<EMAIL>")
}

#Preview("ProfileActionButton - Primary") {
    ProfileActionButton(
        title: "Primary Action",
        systemImage: "star.fill",
        backgroundColor: .blue,
        foregroundColor: .white,
        action: {}
    )
    .padding()
}

#Preview("ProfileActionButton - Secondary") {
    ProfileActionButton(
        title: "Secondary Action",
        systemImage: "gear",
        backgroundColor: Color.gray.opacity(0.1),
        foregroundColor: .primary,
        action: {}
    )
    .padding()
}
