// swift-tools-version: 5.10
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "Template",
    platforms: [
        .iOS(.v16),
        .macOS(.v13),
    ],
    products: [
        .library(
            name: "AppFeatures",
            targets: ["AppFeatures"]
        ),
        .library(
            name: "AppResources",
            targets: ["AppResources"]
        ),
        .library(
            name: "Network",
            targets: ["Network"]
        ),
        .library(
            name: "Extensions",
            targets: ["Extensions"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/shanegao/swiftui-extensions.git", branch: "master"),
        .package(url: "https://github.com/pointfreeco/swift-composable-architecture", from: "1.19.0"),
        .package(url: "https://github.com/onevcat/Kingfisher", from: "7.12.0"),
        .package(url: "https://github.com/vtourraine/AcknowList", from: "3.2.0"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "AppFeatures",
            dependencies: [
                .appResources,
                .extensions,
                .network,
                .composableArchitecture,
                .kingfisher,
                .swiftuiExtensions,
                .acknowList
            ]
        ),
        .target(
            name: "AppResources",
            resources: [
                .process("Resources/"),
                .process("Fonts/"),
            ]
        ),

        .target(
            name: "Network",
            dependencies: [
                .extensions,
                .swiftuiExtensions,
                .composableArchitecture,
                .appResources
            ]
        ),

        .target(
            name: "Extensions",
            dependencies: [
                .composableArchitecture
            ]
        ),

        .testTarget(
            name: "AppFeaturesTests",
            dependencies: ["AppFeatures"]
        ),

        .testTarget(
            name: "NetworkTests",
            dependencies: ["Network"]
        ),

        .testTarget(
            name: "ExtensionsTests",
            dependencies: ["Extensions"]
        ),
    ]
)

extension Target.Dependency {
    // Local
    static let appResources = Target.Dependency(stringLiteral: "AppResources")
    static let network = Target.Dependency(stringLiteral: "Network")
    static let extensions = Target.Dependency(stringLiteral: "Extensions")

    // External
    static let kingfisher = product(name: "Kingfisher", package: "Kingfisher")
    static let composableArchitecture = product(name: "ComposableArchitecture", package: "swift-composable-architecture")
    static let swiftuiExtensions = product(name: "SwiftUIExtensions", package: "swiftui-extensions")
    static let acknowList = product(name: "AcknowList", package: "AcknowList")
}
